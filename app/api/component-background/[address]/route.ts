import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

// GET background color for a component
export async function GET(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;
    const { searchParams } = new URL(request.url);
    const componentType = searchParams.get('componentType');

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    if (!componentType) {
      return Response.json(
        { error: 'Component type is required' },
        { status: 400 }
      );
    }

    // Get the component's background color from details field
    const component = await db
      .select({ details: componentPositions.details })
      .from(componentPositions)
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType)
        )
      );

    if (component.length === 0) {
      return Response.json(
        { error: 'Component not found' },
        { status: 404 }
      );
    }

    // Extract backgroundColor from details
    const details = component[0].details || {};

    // Use the backgroundColor from details with a type assertion
    const backgroundColor = (details as any).backgroundColor;

    return Response.json({
      backgroundColor
    });
  } catch (error) {
    console.error('Failed to fetch component background color:', error);
    return Response.json(
      { error: 'Failed to fetch component background color' },
      { status: 500 }
    );
  }
}

// POST to update background color
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    const data = await request.json();
    const { componentType, backgroundColor, chain } = data;

    if (!componentType) {
      return Response.json(
        { error: 'Component type is required' },
        { status: 400 }
      );
    }

    if (!backgroundColor) {
      return Response.json(
        { error: 'Background color is required' },
        { status: 400 }
      );
    }

    // First check if the component exists
    const existingComponent = await db
      .select()
      .from(componentPositions)
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType),
          eq(componentPositions.chain, chain)
        )
      );

    if (existingComponent.length === 0) {
      return Response.json(
        { error: 'Component not found' },
        { status: 404 }
      );
    }

    // Get existing details
    const existingDetails = existingComponent[0].details || {} as any;

    // Update the component background color in details field
    await db
      .update(componentPositions)
      .set({
        details: {
          ...existingDetails,
          backgroundColor
        },
        updatedAt: new Date()
      })
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType),
          eq(componentPositions.chain, chain)
        )
      );

    // Get the updated component
    const updatedComponent = await db
      .select()
      .from(componentPositions)
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType),
          eq(componentPositions.chain, chain)
        )
      );

    return Response.json({
      message: 'Component background color updated successfully',
      component: updatedComponent[0]
    });
  } catch (error) {
    console.error('Failed to update component background color:', error);
    return Response.json(
      { error: 'Failed to update component background color' },
      { status: 500 }
    );
  }
}
